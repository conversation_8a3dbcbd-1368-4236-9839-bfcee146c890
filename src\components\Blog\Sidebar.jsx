import { Link } from "react-router-dom";
// --- تم تحديث استيراد الصور لتتوافق مع معايير ES Modules الحديثة ---
import thumb6 from '@images/imgs/blog/6/thumb.webp';
import thumb7 from '@images/imgs/blog/7/thumb.webp';
import thumb8 from '@images/imgs/blog/8/thumb.webp';
import thumb9 from '@images/imgs/blog/9/thumb.webp';
import thumb10 from '@images/imgs/blog/10/thumb.webp';
// ---------------------------------------------------

const Sidebar = () => {
  return (
    <aside className="side-bar">
      <div className="section-title">
        <h2>مواضيع تهمك</h2>
      </div>
      <div className="top-topics-wraper">
        <div className="topic">
          <div className="topic-img">
            <Link to="/blog6">
              <img src={thumb6} alt="العناصر الدقيقة وأهميتها للصحة" />
            </Link>
          </div>
          <div className="topic-title">
            <Link to="/blog6">العناصر الدقيقة: مفاتيح القوة الخفية في جسمك</Link>
          </div>
        </div>

        <div className="topic">
          <div className="topic-img">
            <Link to="/blog7">
              <img src={thumb7} alt="علامات نقص التغذية الخفية" />
            </Link>
          </div>
          <div className="topic-title">
            <Link to="/blog7">إنذارات جسمك المهملة: علامات نقص التغذية</Link>
          </div>
        </div>

        <div className="topic">
          <div className="topic-img">
            <Link to="/blog8">
              <img src={thumb8} alt="المكملات الغذائية حلول فعالة أم استبدال خاطئ" />
            </Link>
          </div>
          <div className="topic-title">
            <Link to="/blog8">المكملات الغذائية: معجزة العصر أم خدعة تسويقية؟</Link>
          </div>
        </div>

        <div className="topic">
          <div className="topic-img">
            <Link to="/blog9">
              <img src={thumb9} alt="فهم عميق للصحة والجسم البشري" />
            </Link>
          </div>
          <div className="topic-title">
            <Link to="/blog9">أسرار الجسم البشري: دليل المبتدئين للصحة المثالية</Link>
          </div>
        </div>

        <div className="topic">
          <div className="topic-img">
            <Link to="/blog10">
              <img src={thumb10} alt="كيف تتحكم بمعدل حرقك" />
            </Link>
          </div>
          <div className="topic-title">
            <Link to="/blog10">اشعل محرك جسمك: دليل التحكم في معدل الحرق</Link>
          </div>
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;
